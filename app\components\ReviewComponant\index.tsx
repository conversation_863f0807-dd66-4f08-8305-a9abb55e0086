// Upload.js
import React from 'react';
import { ScrollView, View } from 'react-native';
import Button from '@components/UI/Button';
import styles from './styles';
import { translate } from '@language/Translate';
import ApplicantDetailComponant from '@components/ApplicantDetailComponant';
import { useSelector } from 'react-redux';
import AnimatedView from '@components/AnimatedView';
import SetupSteps from '@components/SetupSteps';

export default function ReviewComponant({
  handleSubmit,
  name,
  lastname,
  location,
  profileImage,
  selectedTags,
  about,
  navigation,
  setIsAvailable,
  isAvailable,
  experienceList,
  setExperienceList,
  certificationList,
  setCertificationList,
  cvFile,
  deleteFile,
  loader,
  Review,
  licenseFiles,
  setPreCertificate,
  preCertificate,
  setPreExperince,
  preExperince,
  isOwnProfileScreen,
  // orderType,
  // previousScreen,
  // currentStep,
  aiDescription,
}: any) {
  const { userData } = useSelector((state: any) => state.auth);

  const handleNext = () => {
    let isValid = true;
    if (isValid) {
      handleSubmit();
    }
  };
  const isProfileFullyCompleted =
    userData?.isProfileSet === true &&
    userData?.personaStatus === 'approved' &&
    userData?.bankAccountVerified === 'verified';
  return (
    <View style={{ flex:1 }}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        style={{
          // flex: 1,
        }}>
        <AnimatedView>
          {isOwnProfileScreen ? (
            <>
              {!isProfileFullyCompleted ? (
                <View>
                  <SetupSteps navigation={navigation} />
                </View>
              ) : null}
            </>
          ) : null}
          <ApplicantDetailComponant
            reviewType="review"
            name={name}
            lastname={lastname}
            profileImage={profileImage}
            location={location}
            about={about}
            selectedTags={selectedTags}
            type="OWN"
            id={userData?.id}
            navigation={navigation}
            setIsAvailable={setIsAvailable}
            isAvailable={isAvailable}
            certificationList={certificationList}
            setCertificationList={setCertificationList}
            experienceList={experienceList}
            setExperienceList={setExperienceList}
            cvFile={cvFile}
            userDetails={userData}
            deleteFile={deleteFile}
            loader={loader}
            Review={Review}
            licenseFiles={licenseFiles}
            setPreCertificate={setPreCertificate}
            preCertificate={preCertificate}
            setPreExperince={setPreExperince}
            preExperince={preExperince}
            aiDescription={aiDescription}
          />
        </AnimatedView>
      </ScrollView>
      {!isOwnProfileScreen ? (
        <View style={styles.nextButtonContainer}>
          <Button type="text" onPress={handleNext} loading={false}>
            {translate('confirm', '')}
          </Button>
        </View>
      ) : null}
    </View>
  );
}
